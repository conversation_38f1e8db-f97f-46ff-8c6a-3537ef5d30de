import { requestClient } from '#/api/request';

/**
 * 点数配置接口
 */
export interface TallySetConfig {
  ai_create_deduct: string;
  ai_title_create_deduct: string;
  ai_video_extraction: string;
  avatar_deduct: string;
  avatar_deduct_four: string;
  avatar_high_deduct: string;
  avatar_high_deduct_two: string;
  clip_price: string;
  composite_deduct: string;
  composite_deduct_four: string;
  douyin_authorize: string;
  douyin_homepage: string;
  douyin_homepage_video: string;
  douyin_scan_code: string;
  douyin_video: string;
  face_deduct: string;
  high_fidelity_sound: string;
  kuaishou_video: string;
  photo_deduct: string;
  photo_high_deduct: string;
  shipinghao_authorize: string;
  shipinghao_video: string;
  shipinhao_scan_code: string;
  video_deduct: string;
  voice_deduct: string;
  voice_high_deduct: string;
  woni_video_deduct: string;
  xiaohongshu_authorize: string;
  xiaohongshu_scan_code: string;
  xiaohongshu_video: string;
  xunfei_sound_generate: string;
  xunfei_sound_train: string;
}

/**
 * 获取点数配置
 * @returns Promise<TallySetConfig>
 */
export function getTallySetConfig(): Promise<TallySetConfig> {
  return requestClient.post('/mobile/index/tallySet');
}

/**
 * 音色克隆请求参数接口
 */
export interface VoiceCloneParams {
  name: string; // 克隆标题
  voice_urls: string[]; // 音频文件URL数组
}

/**
 * 入门版音色克隆
 * @param params 克隆参数
 * @returns Promise<any>
 */
export function voiceTraining(params: VoiceCloneParams): Promise<any> {
  return requestClient.post('/mobile/voice/training', params);
}

/**
 * 高保真音色克隆请求参数接口（新版本）
 */
export interface MegaTtsCloneParams {
  title: string; // 克隆标题
  tts: File; // 音频文件本身
}

/**
 * 高保真音色克隆（旧版本，保留兼容性）
 * @param params 克隆参数
 * @returns Promise<any>
 */
export function voiceTtsUpload(params: VoiceCloneParams): Promise<any> {
  return requestClient.post('/mobile/voice/ttsUpload', params);
}

/**
 * 高保真音色克隆（新版本）
 * @param params 克隆参数
 * @returns Promise<any>
 */
export function megaTtsClone(params: MegaTtsCloneParams): Promise<any> {
  const formData = new FormData();
  formData.append('title', params.title);
  formData.append('tts', params.tts);

  return requestClient.post('/mobile/mega_tts/copy', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 高保真音色列表请求参数接口
 */
export interface MegaTtsListParams {
  page: number; // 页码
  psize: number; // 每页数量
}

/**
 * 高保真音色列表项接口
 */
export interface MegaTtsListItem {
  id: number;
  uid: number;
  title: string;
  status: string; // 状态：成功、失败等
  speaker_id: string;
  demo_audio: string; // 试听音频链接
  type: number;
  create_time: string;
  update_time: string;
}

/**
 * 高保真音色列表响应接口
 */
export interface MegaTtsListResponse {
  list: MegaTtsListItem[];
  total: number;
  pindex: string;
  psize: string;
  totalPage: number;
}

/**
 * 高保真音色删除请求参数接口
 */
export interface MegaTtsDeleteParams {
  id: number; // 音色ID
}

/**
 * 获取高保真音色列表
 * @param params 请求参数
 * @returns Promise<MegaTtsListResponse>
 */
export function getMegaTtsList(
  params: MegaTtsListParams,
): Promise<MegaTtsListResponse> {
  return requestClient.post('/mobile/mega_tts/list', params);
}

/**
 * 删除高保真音色
 * @param params 删除参数
 * @returns Promise<any>
 */
export function deleteMegaTts(params: MegaTtsDeleteParams): Promise<any> {
  return requestClient.post('/mobile/mega_tts/del', params);
}

/**
 * 上传音频文件
 * @param file 音频文件
 * @returns Promise<string> 返回文件URL
 */
export function uploadAudioFile(file: File): Promise<string> {
  return requestClient.upload('/mobile/index/upload', {
    file,
  });
}
