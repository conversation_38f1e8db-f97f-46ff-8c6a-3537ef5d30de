import { requestClient } from '#/api/request';

/**
 * 语音克隆请求参数接口
 */
export interface VoiceCloneParams {
  msg: string; // 文本内容
  pitchRate: string; // 音量 0-100
  speechRate: string; // 语速 0-100
  voice_id: string; // 选中的音色ID
}

/**
 * 入门版语音克隆请求参数接口
 */
export interface SendTtsParams {
  msg: string; // 文本内容
  pitchRate: string; // 音量 0-100
  speechRate: string; // 语速 0-100
  voice_id: string; // 选中的音色ID
  name: string; // 音色名称
  voice_type: number; // 固定值: 2
}

/**
 * 高保真语音克隆请求参数接口（旧版本）
 */
export interface TtsSsmlParams {
  text: string; // SSML格式的文本内容
  voice_id: string; // 选中的音色ID
}

/**
 * 高保真音频合成请求参数接口（新版本）
 */
export interface MegaTtsSynthesizeParams {
  speaker_id: string; // 音色的speaker_id
  type: number; // 1自己的 2公共音色
  content: string; // 文本内容
  speed_ratio: number; // 语速 0.2-3，默认1
  loudness_ratio: number; // 音量 0.5-2，默认1
}

/**
 * 公共音色详情接口
 */
export interface PublicVoiceDetails {
  demo_link: string; // 试听音频链接
  language: string; // 语言
  recommended_scenario: string; // 推荐场景
  tone_number: string; // 音色编号
  voice_type: string; // 音色类型
}

/**
 * 公共音色项接口
 */
export interface PublicVoiceItem {
  instance_number: string;
  is_shareable: boolean;
  resource_id: string;
  code: string;
  configuration_code: string;
  resource_display: string; // 音色名称
  raw_type: string;
  type: string;
  pack_type: string;
  purchased_amount: string;
  current_usage: string;
  expires: string;
  details: PublicVoiceDetails;
  group_name: string;
  alias: string;
  train_id: string;
  state: string;
}

/**
 * 语音克隆返回接口
 */
export interface VoiceCloneResult {
  id: string;
  url: string;
}

/**
 * 音频刷新请求参数接口
 */
export interface SoundRefreshParams {
  sound_id: string; // 入门版声音克隆返回的字符串
}

/**
 * 添加声音请求参数接口
 */
export interface SoundAddParams {
  name: string; // 标题
  url: string; // 第一个接口返回的URL
}
/**
 * 生成视频请求参数接口 (线路二、三使用)
 */
export interface GenerateVideoParams {
  caption_josn: string; // 空字符串
  combination: string; // 空字符串
  height: number | string; // 数字人封面的高
  image_id: string; // 选择的数字人ID
  name: string; // 视频名称
  sound_id: string; // 第二个接口返回的data
  width: number | string; // 数字人封面的宽
}

/**
 * 生成视频请求参数接口 (线路一、四使用)
 */
export interface GenerateVideoNewParams {
  name: string; // 视频标题
  avatar_id: string; // 数字人id
  sound_id: string; // 如果是入门版需要填写soundAddApi接口的返回值，否则为空
  audio_src: string; // 音频链接，如果是入门版声音则为空
}

/**
 * 语音克隆API (专业版)
 * @param params 请求参数
 * @returns Promise<VoiceCloneResult>
 */
export function voiceCloneApi(
  params: VoiceCloneParams,
): Promise<VoiceCloneResult> {
  return requestClient.post('/mobile/voice/voiceClone', params);
}

/**
 * 入门版语音克隆API
 * @param params 请求参数
 * @returns Promise<string> 返回sound_id字符串
 */
export function sendTtsApi(params: SendTtsParams): Promise<string> {
  return requestClient.post('/mobile/video/sendTts', params);
}

/**
 * 音频刷新API (用于入门版声音克隆后检查音频文件状态)
 * @param params 请求参数
 * @returns Promise<string> 返回音频文件URL字符串
 */
export function soundRefreshApi(params: SoundRefreshParams): Promise<string> {
  return requestClient.post('/mobile/video/soundRefresh', params);
}

/**
 * 高保真语音克隆API（旧版本）
 * @param params 请求参数
 * @returns Promise<VoiceCloneResult>
 */
export function ttsSsmlApi(params: TtsSsmlParams): Promise<VoiceCloneResult> {
  return requestClient.post('/mobile/voice/ttsSsml', params);
}

/**
 * 高保真音频合成API（新版本）
 * @param params 请求参数
 * @returns Promise<{ voice_url: string }>
 */
export function megaTtsSynthesizeApi(
  params: MegaTtsSynthesizeParams,
): Promise<{ voice_url: string }> {
  return requestClient.post('/mobile/mega_tts/synthesize', params);
}

/**
 * 获取公共音色列表API
 * @returns Promise<PublicVoiceItem[]>
 */
export function getPublicVoiceListApi(): Promise<PublicVoiceItem[]> {
  return requestClient.get('/json/voice_pack.json');
}

/**
 * 添加声音API
 * @param params 请求参数
 * @returns Promise<SoundAddResult>
 */
export function soundAddApi(params: SoundAddParams): Promise<string> {
  return requestClient.post('/mobile/composite/soundAdd', params);
}

/**
 * 生成视频API (线路二使用)
 * @param params 请求参数
 * @returns Promise<any>
 */
export function generateVideoApi(params: GenerateVideoParams): Promise<any> {
  return requestClient.post('/mobile/video/generateTwo', params);
}

/**
 * 生成视频API (线路三使用)
 * @param params 请求参数
 * @returns Promise<any>
 */
export function generateCompositVideoApi(
  params: GenerateVideoParams,
): Promise<any> {
  return requestClient.post('/mobile/composite/generate', params);
}

/**
 * 生成视频API (线路一使用)
 * @param params 请求参数
 * @returns Promise<any>
 */
export function generateVideoOneApi(
  params: GenerateVideoNewParams,
): Promise<any> {
  return requestClient.post('/mobile/video/generate', params);
}

/**
 * 生成视频API (线路四使用)
 * @param params 请求参数
 * @returns Promise<any>
 */
export function generateVideoFourApi(
  params: GenerateVideoNewParams,
): Promise<any> {
  return requestClient.post('/mobile/video/generateFour', params);
}

/**
 * 获取入门版声音克隆的音频链接 (轮询直到获得结果)
 * @param soundId 入门版声音克隆返回的sound_id字符串
 * @param uid 用户ID
 * @param maxAttempts 最大尝试次数 (默认10次，共40秒)
 * @returns Promise<string> 返回音频文件URL字符串
 */
export async function pollSoundRefresh(
  soundId: string,
  uid: number,
  maxAttempts: number = 50,
): Promise<string> {
  return new Promise((resolve, reject) => {
    let attempts = 0;

    const checkAudio = async () => {
      try {
        const url = await soundRefreshApi({ sound_id: soundId, uid });

        // 如果返回的URL不为空，则表示音频已准备好
        if (url && typeof url === 'string' && url.trim() !== '') {
          resolve(url);
          return;
        }

        // 增加尝试次数
        attempts++;

        // 如果达到最大尝试次数，则失败
        if (attempts >= maxAttempts) {
          reject(new Error('获取音频文件超时，请重试'));
          return;
        }

        // 每4秒检查一次
        setTimeout(checkAudio, 4000);
      } catch (error) {
        reject(error);
      }
    };

    // 开始检查
    checkAudio();
  });
}
