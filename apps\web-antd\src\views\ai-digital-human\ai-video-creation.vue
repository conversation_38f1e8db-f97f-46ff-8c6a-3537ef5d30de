<script lang="ts" setup>
import type {
  AvatarListItem,
  CloneSetConfig,
  WayInfo,
} from '#/api/core/digital-human';
import type { PublicVoiceItem } from '#/api/core/video-creation';

import { computed, h, onMounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';

import {
  Button,
  Card,
  Checkbox,
  Drawer,
  Input,
  message,
  Modal,
  Pagination,
  Slider,
  Tabs,
} from 'ant-design-vue';

import {
  getAvatarList,
  getCloneSetConfig,
  getVoiceTrainList,
  getWayList,
} from '#/api/core/digital-human';
import {
  generateCompositVideoApi,
  generateVideoApi,
  generateVideoFourApi,
  generateVideoOneApi,
  megaTtsSynthesizeApi,
  pollSoundRefresh,
  sendTtsApi,
  soundAddApi,
  voiceCloneApi,
} from '#/api/core/video-creation';
import { getMegaTtsList } from '#/api/core/voice-clone';

import { createStreamHandler } from '../../utils/stream-request';
import publicVoiceData from './public-voice.json';

// 定义组件名称
defineOptions({ name: 'AIVideoCreation' });

// 路由
const router = useRouter();
const route = useRoute();

// 响应式数据
const loading = ref(false);
const aiRewriteLoading = ref(false);

// 抽屉状态
const avatarDrawerVisible = ref(false);
const voiceDrawerVisible = ref(false);
const publicVoiceModalVisible = ref(false); // 公共音色弹窗状态

// 试听功能状态
const previewLoading = ref(false); // 音频合成loading状态
const previewAudio = ref<HTMLAudioElement | null>(null); // 音频播放器实例
const previewAudioUrl = ref(''); // 当前试听音频URL
const isPlaying = ref(false); // 是否正在播放
const hasGenerated = ref(false); // 是否已生成过音频

// 表单数据
const formData = ref({
  selectedAvatar: '', // 选择的数字人
  videoTitle: '', // 视频标题
  textContent: '', // 文本内容
  selectedVoice: '', // 音色选择
  agreeToTerms: false, // 同意协议
  speechRate: '50', // 语速 0-100（对应高保真的1.0）
  pitchRate: '50', // 音量 0-100（对应高保真的1.0）
});

// 语速和音量转换函数（用于高保真音色）
const convertSpeedRatio = (value: string): number => {
  // 将0-100的值转换为0.2-3的范围，50对应1.0
  const numValue = Number.parseInt(value);
  if (numValue <= 50) {
    // 0-50 映射到 0.2-1.0
    return 0.2 + (numValue / 50) * 0.8;
  } else {
    // 50-100 映射到 1.0-3.0
    return 1 + ((numValue - 50) / 50) * 2;
  }
};

const convertLoudnessRatio = (value: string): number => {
  // 将0-100的值转换为0.5-2的范围，50对应1.0
  const numValue = Number.parseInt(value);
  if (numValue <= 50) {
    // 0-50 映射到 0.5-1.0
    return 0.5 + (numValue / 50) * 0.5;
  } else {
    // 50-100 映射到 1.0-2.0
    return 1 + ((numValue - 50) / 50) * 1;
  }
};

// 配置数据
const cloneSetConfig = ref<CloneSetConfig | null>(null);
const configLoading = ref(false);

// 数字人相关数据
const wayList = ref<WayInfo[]>([]);
const avatarList = ref<AvatarListItem[]>([]);
const avatarLoading = ref(false);
const currentAvatarWayId = ref(0); // 当前选中的数字人线路ID
const avatarPagination = ref({
  current: 1,
  pageSize: 6,
  total: 0,
});

// 音色相关数据
const voiceList = ref<any[]>([]);
const voiceLoading = ref(false);
const currentVoiceModeId = ref(1); // 当前选中的音色模式ID
const voicePagination = ref({
  current: 1,
  pageSize: 9,
  total: 0,
});

// 公共音色相关数据
const publicVoiceList = ref<PublicVoiceItem[]>([]);
const publicVoiceLoading = ref(false);
const selectedPublicVoice = ref<null | PublicVoiceItem>(null); // 选中的公共音色
const publicVoiceScenarios = ref<string[]>([]); // 场景筛选选项
const selectedScenario = ref(''); // 选中的场景
const playingAudioUrl = ref(''); // 当前播放的音频URL
const audioPlayer = ref<HTMLAudioElement | null>(null); // 音频播放器实例

// 音色模式选项（动态计算属性）
const voiceModeOptions = computed(() => {
  const options = [{ id: 1, name: '入门版' }];

  // 根据xunfei_sound_clone_swich字段决定是否显示专业版选项
  if (cloneSetConfig.value?.xunfei_sound_clone_swich === 1) {
    options.push({ id: 3, name: '专业版' });
  }

  // 根据voice_high_open字段决定是否显示高保真选项
  if (cloneSetConfig.value?.voice_high_open === 1) {
    options.push({ id: 2, name: '高保真' });
  }

  return options;
});

// 参数对象，用于存储当前选中的数字人信息
const param = ref({
  base_video: '', // 数字人形象视频URL
  decode_img: '', // 数字人形象图片URL
  id: '', // 选中的数字人ID
  isSel: '', // 当前选中的线路ID (1=线路一, 2=线路二, 3=线路三, 4=线路四)
  current_status: '', // 线路一状态
  new_current_status: '', // 线路二状态
  composite_current_status: '', // 线路三状态
  four_current_status: '', // 线路四状态
});

// 列表对象，用于存储选中的数字人完整信息
const listObj = ref<AvatarListItem | null>(null);

// 图片尺寸信息
const web_people_width = ref('');
const web_people_height = ref('');

// 计算属性
// 表单验证
const canSubmit = computed(() => {
  return (
    formData.value.selectedAvatar.trim() && // 数字人必选
    formData.value.videoTitle.trim() && // 标题必填
    formData.value.textContent.trim() && // 文本必填
    formData.value.selectedVoice.trim() && // 音色必选
    formData.value.agreeToTerms && // 协议必须同意
    !loading.value
  );
});

// 试听按钮可用状态
const canPreview = computed(() => {
  return (
    formData.value.selectedVoice.trim() && // 音色必选
    formData.value.textContent.trim() && // 文本必填
    !previewLoading.value // 不在合成中
  );
});

// 试听按钮文字
const previewButtonText = computed(() => {
  if (previewLoading.value) {
    return '合成中...';
  }
  if (isPlaying.value) {
    return '停止播放';
  }
  if (hasGenerated.value) {
    return '重新生成';
  }
  return '试听';
});

// 是否显示语速音量控件（只有高保真音色才显示）
const showVoiceControls = computed(() => {
  if (!formData.value.selectedVoice) return false;

  const selectedVoice = getSelectedVoiceType();
  return selectedVoice?.trainMode === 2; // 高保真音色
});

// 筛选后的公共音色列表
const filteredPublicVoiceList = computed(() => {
  if (!selectedScenario.value) {
    return publicVoiceList.value;
  }
  return publicVoiceList.value.filter(
    (voice) => voice.details.recommended_scenario === selectedScenario.value,
  );
});

// 方法
// 初始化线路数据
const initWayList = async () => {
  try {
    const ways = await getWayList();
    wayList.value = ways;
    console.warn('线路数据获取成功:', ways);
  } catch (error) {
    console.error('线路数据获取失败:', error);
    message.error('线路数据加载失败');
  }
};

// 加载数字人列表（按线路分页加载）
const loadAvatarList = async (wayId?: number, page: number = 1) => {
  try {
    avatarLoading.value = true;

    // 如果没有指定线路ID，使用当前选中的线路
    const targetWayId = wayId ?? currentAvatarWayId.value;

    // 如果还没有选中线路，默认选择第一个线路
    if (targetWayId === 0 && wayList.value.length > 0) {
      currentAvatarWayId.value = wayList.value[0]?.id || 1;
    }

    const finalWayId = targetWayId || currentAvatarWayId.value;
    const definition = finalWayId === 4 ? 2 : 1; // 线路四使用高清，其他使用标清

    // 根据不同线路使用不同的状态参数
    const params: any = {
      definition,
      page,
      psize: avatarPagination.value.pageSize,
      buy_expire: 2,
    };

    // 根据线路ID设置对应的状态参数
    switch (finalWayId) {
      case 1: {
        params.current_status = 'completed';
        break;
      }
      case 2: {
        params.new_current_status = 'completed';
        break;
      }
      case 3: {
        params.composite_current_status = 'completed';
        break;
      }
      case 4: {
        params.four_current_status = 'completed';
        break;
      }
    }

    const response = await getAvatarList(params);
    avatarList.value = response.list;
    avatarPagination.value = {
      current: response.pindex,
      pageSize: response.psize,
      total: response.total,
    };

    // 如果列表不为空且当前没有选中的数字人，设置默认选择
    if (response.list.length > 0 && !param.value.id) {
      param.value.isSel = finalWayId.toString();
    }

    console.warn('数字人列表获取成功:', response);
  } catch (error) {
    console.error('数字人列表获取失败:', error);
    message.error('数字人列表加载失败');
  } finally {
    avatarLoading.value = false;
  }
};

// 加载音色列表（按模式分页加载）
const loadVoiceList = async (modeId?: number, page: number = 1) => {
  try {
    voiceLoading.value = true;

    // 如果没有指定模式ID，使用当前选中的模式
    const targetModeId = modeId ?? currentVoiceModeId.value;

    if (targetModeId === 2) {
      // 高保真音色：使用新接口
      const params = {
        page,
        psize: voicePagination.value.pageSize,
      };

      const response = await getMegaTtsList(params);
      voiceList.value = response.list;
      voicePagination.value = {
        current: Number.parseInt(response.pindex),
        pageSize: Number.parseInt(response.psize),
        total: response.total,
      };
      console.warn('高保真音色列表获取成功:', response);
    } else {
      // 入门版和专业版：使用原有接口
      const params = {
        name: '',
        page,
        psize: voicePagination.value.pageSize,
        train_mode: targetModeId,
        buy_expire: 2,
        current_status: 'completed',
      };

      const response = await getVoiceTrainList(params);
      voiceList.value = response.list;
      voicePagination.value = {
        current: response.pindex,
        pageSize: response.psize,
        total: response.total,
      };
      console.warn('音色列表获取成功:', response);
    }
  } catch (error) {
    console.error('音色列表获取失败:', error);
    message.error('音色列表加载失败');
  } finally {
    voiceLoading.value = false;
  }
};

// 初始化配置
const initConfigs = async () => {
  try {
    configLoading.value = true;

    // 获取协议配置
    const cloneConfig = await getCloneSetConfig();
    cloneSetConfig.value = cloneConfig;

    console.warn('配置初始化成功:', { cloneConfig });
  } catch (error) {
    console.error('配置初始化失败:', error);
    message.error('配置加载失败，请刷新页面重试');
  } finally {
    configLoading.value = false;
  }
};

// 查看用户协议
const viewUserAgreement = () => {
  if (cloneSetConfig.value?.composite_notice) {
    // 显示协议内容弹窗
    Modal.info({
      title: '视频合成协议',
      content: h('div', {
        innerHTML: cloneSetConfig.value.composite_notice,
      }),
      width: 600,
      okText: '我知道了',
    });
  } else {
    message.warning('协议内容加载中，请稍后重试');
  }
};

// 获取选中的数字人名称
const getSelectedAvatarName = () => {
  return listObj.value ? listObj.value.name : '';
};

// 获取选中的音色完整名称（包含分类）
const getSelectedVoiceFullName = () => {
  // 检查是否是公共音色
  if (
    formData.value.selectedVoice.startsWith('public_') &&
    selectedPublicVoice.value
  ) {
    const modeName = '高保真公共音色';
    const voiceName = selectedPublicVoice.value.resource_display;
    return `已选择：${modeName}-${voiceName}`;
  }

  const selected = voiceList.value.find(
    (item) => item.id.toString() === formData.value.selectedVoice,
  );

  if (!selected) return '';

  // 高保真音色使用不同的数据结构
  if (currentVoiceModeId.value === 2) {
    const modeName = '高保真';
    const voiceName = selected.title || selected.name || '音色';
    return `已选择：${modeName}-${voiceName}`;
  }

  // 入门版和专业版使用原有逻辑
  const mode = voiceModeOptions.value.find(
    (item: any) => item.id === selected.train_mode,
  );
  const modeName = mode ? mode.name : '';
  return `已选择：${modeName}-${selected.name}`;
};

// 获取选中的音色类型
const getSelectedVoiceType = (): null | {
  isPublic?: boolean;
  name: string;
  speakerId?: string;
  trainMode: number;
} => {
  // 检查是否是公共音色
  if (
    formData.value.selectedVoice.startsWith('public_') &&
    selectedPublicVoice.value
  ) {
    return {
      trainMode: 2, // 公共音色也使用高保真模式
      name: selectedPublicVoice.value.resource_display,
      speakerId: selectedPublicVoice.value.details.voice_type,
      isPublic: true,
    };
  }

  const selected = voiceList.value.find(
    (item) => item.id.toString() === formData.value.selectedVoice,
  );

  if (!selected) return null;

  // 高保真音色使用不同的数据结构
  if (currentVoiceModeId.value === 2) {
    return {
      trainMode: 2,
      name: selected.title || selected.name || '高保真音色',
      speakerId: selected.speaker_id,
      isPublic: false,
    };
  }

  // 入门版和专业版使用原有结构
  return {
    trainMode: selected.train_mode,
    name: selected.name,
    isPublic: false,
  };
};

// 获取图片尺寸的方法
const getImage = (imageUrl: string) => {
  const image = new Image();
  image.addEventListener('load', () => {
    web_people_width.value = image.width.toString();
    web_people_height.value = image.height.toString();
    console.warn('图片尺寸获取成功:', {
      width: web_people_width.value,
      height: web_people_height.value,
      url: imageUrl,
    });
  });
  image.addEventListener('error', () => {
    console.error('图片加载失败:', imageUrl);
  });
  image.src = imageUrl;
};

// 切换数字人线路
const switchAvatarWay = async (wayId: number | string) => {
  const id = typeof wayId === 'string' ? Number.parseInt(wayId) : wayId;

  // 重置数字人相关数据
  listObj.value = null;
  web_people_width.value = '';
  web_people_height.value = '';
  param.value = {
    ...param.value, // 保留其他必要属性
    decode_img: '',
    id: '',
    isSel: id.toString(), // 设置为新的线路ID
  };

  // 重置表单中的数字人选择
  formData.value.selectedAvatar = '';

  currentAvatarWayId.value = id;
  avatarPagination.value.current = 1; // 重置到第一页
  await loadAvatarList(id, 1);
};

// 数字人分页变化
const handleAvatarPageChange = async (page: number) => {
  await loadAvatarList(currentAvatarWayId.value, page);
};

// 切换音色模式
const switchVoiceMode = async (modeId: number | string) => {
  const id = typeof modeId === 'string' ? Number.parseInt(modeId) : modeId;

  // 重置表单中的音色选择
  formData.value.selectedVoice = '';

  currentVoiceModeId.value = id;
  voicePagination.value.current = 1; // 重置到第一页
  await loadVoiceList(id, 1);
};

// 音色分页变化
const handleVoicePageChange = async (page: number) => {
  await loadVoiceList(currentVoiceModeId.value, page);
};

// 打开数字人选择抽屉
const openAvatarDrawer = async () => {
  avatarDrawerVisible.value = true;
  // 打开抽屉时加载数字人列表
  await loadAvatarList();
};

// 打开音色选择抽屉
const openVoiceDrawer = async () => {
  voiceDrawerVisible.value = true;
  // 打开抽屉时加载音色列表
  await loadVoiceList();
};

// 选择数字人
const selectAvatar = (value: string) => {
  // 找到选中的数字人对象
  const selectedAvatar = avatarList.value.find(
    (item) => item.id.toString() === value,
  );

  if (selectedAvatar) {
    // 更新 listObj
    listObj.value = selectedAvatar;

    // 更新 param
    param.value = {
      ...param.value, // 保留其他属性
      decode_img: selectedAvatar.video_cover || '', // 使用数字人的封面图片
      id: selectedAvatar.id.toString(), // 设置数字人ID
      isSel: param.value.isSel, // 保持当前线路不变
      current_status: selectedAvatar.current_status || '',
      new_current_status: selectedAvatar.new_current_status || '',
      composite_current_status: selectedAvatar.composite_current_status || '',
      four_current_status: selectedAvatar.four_current_status || '',
    };

    // 如果是线路二或线路三，需要获取图片尺寸
    const currentWayId = Number.parseInt(param.value.isSel);
    if ((currentWayId === 2 || currentWayId === 3) && param.value.decode_img) {
      getImage(param.value.decode_img);
    }

    console.warn('数字人选择成功:', {
      selectedAvatar,
      param: param.value,
      listObj: listObj.value,
    });
  }

  formData.value.selectedAvatar = value;
  avatarDrawerVisible.value = false;
};

// 选择音色
const selectVoice = (value: string) => {
  formData.value.selectedVoice = value;
  voiceDrawerVisible.value = false;

  // 重置试听状态
  resetPreviewState();

  // 清空公共音色选择
  selectedPublicVoice.value = null;
};

// 加载公共音色列表
const loadPublicVoiceList = async () => {
  try {
    publicVoiceLoading.value = true;

    // 使用本地JSON数据
    const response = publicVoiceData as PublicVoiceItem[];
    publicVoiceList.value = response;

    // 提取并去重场景选项
    const scenarios = [
      ...new Set(
        response.map(
          (voice: PublicVoiceItem) => voice.details.recommended_scenario,
        ),
      ),
    ];
    publicVoiceScenarios.value = scenarios.filter(
      (scenario: string) => scenario && scenario.trim(),
    );

    console.warn('公共音色列表加载成功:', response);
  } catch (error) {
    console.error('公共音色列表加载失败:', error);
    message.error('公共音色列表加载失败');
  } finally {
    publicVoiceLoading.value = false;
  }
};

// 打开公共音色弹窗
const openPublicVoiceModal = async () => {
  // 关闭音色选择抽屉
  voiceDrawerVisible.value = false;

  // 打开公共音色弹窗
  publicVoiceModalVisible.value = true;

  // 加载公共音色列表
  if (publicVoiceList.value.length === 0) {
    await loadPublicVoiceList();
  }
};

// 播放/停止试听音频
const toggleAudioPlay = async (audioUrl: string) => {
  // 如果当前正在播放这个音频，则停止
  if (
    playingAudioUrl.value === audioUrl &&
    audioPlayer.value &&
    !audioPlayer.value.paused
  ) {
    audioPlayer.value.pause();
    playingAudioUrl.value = '';
    return;
  }

  // 停止之前的音频
  if (audioPlayer.value) {
    audioPlayer.value.pause();
  }

  try {
    // 创建新的音频实例
    audioPlayer.value = new Audio(audioUrl);
    playingAudioUrl.value = audioUrl;

    // 设置音频事件监听
    audioPlayer.value.addEventListener('ended', () => {
      playingAudioUrl.value = '';
    });

    audioPlayer.value.addEventListener('error', () => {
      message.error('音频播放失败');
      playingAudioUrl.value = '';
    });

    // 开始播放
    await audioPlayer.value.play();
  } catch (error) {
    console.error('音频播放失败:', error);
    message.error('音频播放失败');
    playingAudioUrl.value = '';
  }
};

// 选择公共音色
const selectPublicVoice = (voice: PublicVoiceItem) => {
  selectedPublicVoice.value = voice;

  // 设置为特殊的公共音色标识
  formData.value.selectedVoice = `public_${voice.details.voice_type}`;

  // 关闭弹窗
  publicVoiceModalVisible.value = false;

  // 重置试听状态
  resetPreviewState();

  message.success(`已选择公共音色：${voice.resource_display}`);
};

// 重置试听状态
const resetPreviewState = () => {
  if (previewAudio.value) {
    previewAudio.value.pause();
    previewAudio.value = null;
  }
  previewAudioUrl.value = '';
  isPlaying.value = false;
  hasGenerated.value = false;
};

// 试听音频合成
const generatePreviewAudio = async () => {
  try {
    previewLoading.value = true;

    // 获取选中的音色信息
    const selectedVoice = getSelectedVoiceType();
    if (!selectedVoice) {
      throw new Error('无法获取音色信息');
    }

    let audioUrl = '';

    // 根据音色类型选择不同的API
    if (selectedVoice.trainMode === 1) {
      // 入门版
      const sendTtsParams = {
        msg: formData.value.textContent,
        pitchRate: formData.value.pitchRate,
        speechRate: formData.value.speechRate,
        voice_id: formData.value.selectedVoice,
        name: selectedVoice.name,
        voice_type: 2,
      };

      // 发送TTS请求获取sound_id
      const soundId = await sendTtsApi(sendTtsParams);
      console.warn('试听-入门版语音克隆初始化成功，开始轮询获取音频:', soundId);

      // 轮询获取音频URL（缩短等待时间）
      await new Promise((resolve) => setTimeout(resolve, 10_000)); // 10秒等待
      audioUrl = await pollSoundRefresh(soundId, 5); // 最多轮询5次
      console.warn('试听-入门版音频URL获取成功:', audioUrl);
    } else if (selectedVoice.trainMode === 2) {
      // 高保真：使用新的synthesize接口
      if (!selectedVoice.speakerId) {
        throw new Error('高保真音色缺少speaker_id');
      }

      const synthesizeParams = {
        speaker_id: selectedVoice.speakerId,
        type: selectedVoice.isPublic ? 2 : 1, // 2公共音色 1自己的音色
        content:
          formData.value.textContent ||
          (selectedVoice.isPublic
            ? '这是公共音色效果演示'
            : '这是你的高保真声音克隆效果，你觉得效果怎么样?'),
        speed_ratio: convertSpeedRatio(formData.value.speechRate),
        loudness_ratio: convertLoudnessRatio(formData.value.pitchRate),
      };

      const synthesizeResult = await megaTtsSynthesizeApi(synthesizeParams);
      console.warn('试听-高保真音频合成成功:', synthesizeResult);
      audioUrl = synthesizeResult.voice_url;
    } else {
      // 专业版(trainMode === 3)或其他
      const voiceCloneParams = {
        msg: formData.value.textContent,
        pitchRate: formData.value.pitchRate,
        speechRate: formData.value.speechRate,
        voice_id: formData.value.selectedVoice,
      };

      const voiceCloneResult = await voiceCloneApi(voiceCloneParams);
      console.warn('试听-专业版语音克隆成功:', voiceCloneResult);
      audioUrl = voiceCloneResult.url;
    }

    // 保存音频URL并开始播放
    previewAudioUrl.value = audioUrl;
    hasGenerated.value = true;
    await playPreviewAudio();
  } catch (error) {
    console.error('试听音频合成失败:', error);
    message.error('音频合成失败，请重试');
  } finally {
    previewLoading.value = false;
  }
};

// 播放试听音频
const playPreviewAudio = async () => {
  if (!previewAudioUrl.value) return;

  try {
    // 创建新的音频实例
    previewAudio.value = new Audio(previewAudioUrl.value);

    // 设置音频事件监听
    previewAudio.value.addEventListener('ended', () => {
      isPlaying.value = false;
      console.warn('试听音频播放完成');
    });

    previewAudio.value.addEventListener('error', (e) => {
      console.error('试听音频播放错误:', e);
      message.error('音频播放失败');
      isPlaying.value = false;
    });

    // 开始播放
    await previewAudio.value.play();
    isPlaying.value = true;
    console.warn('试听音频开始播放');
  } catch (error) {
    console.error('试听音频播放失败:', error);
    message.error('音频播放失败');
    isPlaying.value = false;
  }
};

// 停止试听音频
const stopPreviewAudio = () => {
  if (previewAudio.value) {
    previewAudio.value.pause();
    previewAudio.value.currentTime = 0;
    isPlaying.value = false;
    console.warn('试听音频已停止');
  }
};

// 试听按钮点击处理
const handlePreviewClick = async () => {
  if (previewLoading.value) return;

  if (isPlaying.value) {
    // 正在播放，停止播放
    stopPreviewAudio();
  } else if (hasGenerated.value) {
    // 已生成过，重新生成
    resetPreviewState();
    await generatePreviewAudio();
  } else {
    // 首次生成
    await generatePreviewAudio();
  }
};

// AI仿写功能
const handleAIRewrite = async () => {
  if (!formData.value.textContent) {
    message.warning('请先输入文本内容');
    return;
  }

  try {
    aiRewriteLoading.value = true;
    message.loading('AI正在生成内容...', 0);

    const streamHandler = createStreamHandler();
    let generatedText = '';

    await streamHandler.request({
      url: '/mobile/ai_dialogue/copy',
      method: 'POST',
      body: {
        content: formData.value.textContent,
      },
      onData: (data: string) => {
        try {
          // 尝试解析JSON数据
          const jsonData = JSON.parse(data);
          if (jsonData && jsonData.data) {
            generatedText = jsonData.data;
            // 限制内容长度不超过500个字符
            if (generatedText.length > 500) {
              generatedText = generatedText.slice(0, 500);
            }
            // 在流式返回过程中更新表单内容
            formData.value.textContent = generatedText;
          }
        } catch {
          // 如果不是JSON格式，则直接添加到生成文本中
          generatedText += data;
          // 限制内容长度不超过500个字符
          if (generatedText.length > 500) {
            generatedText = generatedText.slice(0, 500);
          }
          formData.value.textContent = generatedText;
        }
      },
      onComplete: () => {
        message.success('内容生成完成');
        aiRewriteLoading.value = false;
        message.destroy(); // 清除loading消息
      },
      onError: (error: Error) => {
        console.error('AI仿写失败:', error);
        message.error(`生成失败: ${error.message}`);
        aiRewriteLoading.value = false;
        message.destroy(); // 清除loading消息
      },
    });
  } catch (error) {
    console.error('AI仿写功能执行失败:', error);
    const errorMessage =
      error instanceof Error ? error.message : '生成失败，请重试';
    message.error(`生成失败: ${errorMessage}`);
    aiRewriteLoading.value = false;
    message.destroy(); // 清除loading消息
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!canSubmit.value) return;

  // 验证文本内容字数
  if (formData.value.textContent.length > 500) {
    message.error('文本内容不能超过500字，请精简您的内容');
    return;
  }

  try {
    loading.value = true;
    message.loading('正在处理中...', 0);

    console.warn('开始提交视频创作任务');

    // 获取选中的音色信息
    const selectedVoice = getSelectedVoiceType();

    if (!selectedVoice) {
      throw new Error('无法获取音色信息');
    }

    // 声明音频URL变量
    let audioUrl;

    // 根据音色类型选择不同的API
    if (selectedVoice.trainMode === 1) {
      // 入门版
      // 调用入门版语音克隆接口
      const sendTtsParams = {
        msg: formData.value.textContent,
        pitchRate: formData.value.pitchRate,
        speechRate: formData.value.speechRate,
        voice_id: formData.value.selectedVoice,
        name: selectedVoice.name,
        voice_type: 2,
      };

      // 发送TTS请求获取sound_id
      const soundId = await sendTtsApi(sendTtsParams);
      console.warn('入门版语音克隆初始化成功，开始轮询获取音频:', soundId);

      // 轮询获取音频URL
      message.loading('正在合成音频，请稍候...', 0);
      // 30秒后再开始请求
      await new Promise((resolve) => setTimeout(resolve, 30_000));
      audioUrl = await pollSoundRefresh(soundId, 7);
      console.warn('入门版音频URL获取成功:', audioUrl);
    } else if (selectedVoice.trainMode === 2) {
      // 高保真：使用新的synthesize接口
      if (!selectedVoice.speakerId) {
        throw new Error('高保真音色缺少speaker_id');
      }

      const synthesizeParams = {
        speaker_id: selectedVoice.speakerId,
        type: selectedVoice.isPublic ? 2 : 1, // 2公共音色 1自己的音色
        content:
          formData.value.textContent ||
          (selectedVoice.isPublic
            ? '这是公共音色效果演示'
            : '这是你的高保真声音克隆效果，你觉得效果怎么样?'),
        speed_ratio: convertSpeedRatio(formData.value.speechRate),
        loudness_ratio: convertLoudnessRatio(formData.value.pitchRate),
      };

      const synthesizeResult = await megaTtsSynthesizeApi(synthesizeParams);
      console.warn('高保真音频合成成功:', synthesizeResult);
      audioUrl = synthesizeResult.voice_url;
    } else {
      // 专业版(trainMode === 3)或其他
      // 调用专业版语音克隆接口
      const voiceCloneParams = {
        msg: formData.value.textContent,
        pitchRate: formData.value.pitchRate,
        speechRate: formData.value.speechRate,
        voice_id: formData.value.selectedVoice,
      };

      const voiceCloneResult = await voiceCloneApi(voiceCloneParams);
      console.warn('专业版语音克隆成功:', voiceCloneResult);
      audioUrl = voiceCloneResult.url;
    }

    // 根据线路类型和音色类型决定后续流程
    const currentWayId = Number.parseInt(param.value.isSel);

    // 线路一和线路四的新逻辑
    if (currentWayId === 1 || currentWayId === 4) {
      // 线路一和线路四使用新的参数格式
      const isBeginnerVoice = selectedVoice.trainMode === 1; // 入门版

      let soundId = '';
      let audioSrc = '';

      if (isBeginnerVoice) {
        // 入门版：需要调用soundAddApi获取sound_id，audio_src为空
        const soundAddParams = {
          name: formData.value.videoTitle,
          url: audioUrl,
        };
        soundId = await soundAddApi(soundAddParams);
        audioSrc = '';
        console.warn('入门版添加声音成功:', soundId);
      } else {
        // 专业版/高保真：跳过soundAddApi，直接使用音频链接
        soundId = '';
        audioSrc = audioUrl;
        console.warn(
          '专业版/高保真跳过soundAddApi，直接使用音频链接:',
          audioSrc,
        );
      }

      // 使用新的参数格式调用对应的API
      const generateVideoNewParams = {
        name: formData.value.videoTitle,
        avatar_id: formData.value.selectedAvatar,
        sound_id: soundId,
        audio_src: audioSrc,
      };

      if (currentWayId === 1) {
        await generateVideoOneApi(generateVideoNewParams);
        console.warn('生成视频成功（线路一）');
      } else {
        await generateVideoFourApi(generateVideoNewParams);
        console.warn('生成视频成功（线路四）');
      }
    } else {
      // 线路二和线路三使用原有逻辑
      // 步骤2：调用添加声音接口
      const soundAddParams = {
        name: formData.value.videoTitle,
        url: audioUrl,
      };

      const soundAddResult = await soundAddApi(soundAddParams);
      console.warn('添加声音成功:', soundAddResult);

      // 步骤3：调用生成视频接口
      const generateVideoParams = {
        caption_josn: '', // 空字符串
        combination: '', // 空字符串
        height: web_people_height.value || '720', // 数字人封面的高
        image_id: formData.value.selectedAvatar,
        name: formData.value.videoTitle,
        sound_id: soundAddResult,
        width: web_people_width.value || '1280', // 数字人封面的宽
      };

      if (currentWayId === 3) {
        // 线路三使用generate接口
        await generateCompositVideoApi(generateVideoParams);
        console.warn('生成视频成功（线路三）');
      } else {
        // 线路二使用generateTwo接口
        await generateVideoApi(generateVideoParams);
        console.warn('生成视频成功（线路二）');
      }
    }

    // 关闭loading提示
    message.destroy();
    message.success('视频创作任务已提交！');

    // 重置表单
    formData.value = {
      selectedAvatar: '',
      videoTitle: '',
      textContent: '',
      selectedVoice: '',
      agreeToTerms: false,
      speechRate: '50',
      pitchRate: '50',
    };

    // 跳转到数字人页面
    router.push('/public-domain/ai-digital-human');
  } catch (error) {
    // 关闭loading提示
    message.destroy();

    console.error('提交视频创作任务失败:', error);
    const errorMessage =
      error instanceof Error ? error.message : '提交失败，请重试';
    message.error(`提交失败: ${errorMessage}`);
  } finally {
    loading.value = false;
  }
};

// 返回到AI数字人页面
const handleGoBack = () => {
  router.push('/public-domain/ai-digital-human');
};

// 监听配置变化，确保音色模式选择正确
watch(
  [
    () => cloneSetConfig.value?.xunfei_sound_clone_swich,
    () => cloneSetConfig.value?.voice_high_open,
  ],
  ([xunfeiSwitch, voiceHighOpen]) => {
    // 如果专业版被禁用且当前选择的是专业版，则重置为入门版
    if (xunfeiSwitch !== 1 && currentVoiceModeId.value === 3) {
      currentVoiceModeId.value = 1;
      formData.value.selectedVoice = ''; // 清空音色选择
      console.warn('专业版选项已禁用，自动切换到入门版');
    }

    // 如果高保真被禁用且当前选择的是高保真，则重置为入门版
    if (voiceHighOpen !== 1 && currentVoiceModeId.value === 2) {
      currentVoiceModeId.value = 1;
      formData.value.selectedVoice = ''; // 清空音色选择
      selectedPublicVoice.value = null; // 清空公共音色选择
      console.warn('高保真选项已禁用，自动切换到入门版');
    }
  },
  { immediate: true },
);

// 监听文本内容变化，重置试听状态
watch(
  () => formData.value.textContent,
  () => {
    // 文本内容变化时，重置试听状态（因为需要重新生成音频）
    if (hasGenerated.value) {
      resetPreviewState();
    }
  },
);

// 监听语速和音量变化，重置试听状态（仅高保真音色）
watch([() => formData.value.speechRate, () => formData.value.pitchRate], () => {
  // 语速或音量变化时，如果是高保真音色且已生成过音频，则重置试听状态
  if (hasGenerated.value && showVoiceControls.value) {
    resetPreviewState();
  }
});

// 组件挂载时初始化
onMounted(async () => {
  // 并行初始化配置和线路数据
  await Promise.all([initConfigs(), initWayList()]);

  // 检查是否有从热点跟拍页面传递的内容
  if (route.query.content && typeof route.query.content === 'string') {
    formData.value.textContent = route.query.content;
  }

  // 数字人和音色列表在打开抽屉时再加载
});
</script>

<template>
  <Page auto-content-height>
    <div class="ai-video-creation-container">
      <Card class="form-card" :loading="configLoading">
        <template #title>
          <Button type="text" class="back-button" @click="handleGoBack">
            <template #icon>
              <svg
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                class="back-icon"
              >
                <path
                  d="M19 12H5M12 19L5 12L12 5"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </template>
            返回
          </Button>
        </template>

        <div class="form-content">
          <!-- 选择数字人 -->
          <div class="form-section">
            <div class="section-title">选择数字人</div>
            <div class="avatar-selector-box" @click="openAvatarDrawer">
              <div v-if="!formData.selectedAvatar" class="selector-placeholder">
                <div class="placeholder-text">点击选择数字人</div>
              </div>
              <div v-else class="selected-avatar">
                <div class="avatar-preview">
                  <div class="avatar-image">
                    <img
                      v-if="param.decode_img"
                      :src="param.decode_img"
                      alt="数字人封面"
                      class="avatar-cover"
                    />
                    <div v-else class="avatar-placeholder">
                      <div class="placeholder-text">无封面</div>
                    </div>
                  </div>
                </div>
                <div class="avatar-name">{{ getSelectedAvatarName() }}</div>
              </div>
            </div>
          </div>

          <!-- 视频标题 -->
          <div class="form-section">
            <div class="section-title">视频标题</div>
            <Input
              v-model:value="formData.videoTitle"
              placeholder="请输入视频标题"
              :maxlength="50"
              show-count
              class="form-input"
            />
          </div>

          <!-- 文本内容 -->
          <div class="form-section">
            <div class="section-title-with-action">
              <span class="section-title">文本内容</span>
              <Button
                type="link"
                size="small"
                class="ai-rewrite-btn"
                @click="handleAIRewrite"
                :loading="aiRewriteLoading"
              >
                AI仿写
              </Button>
            </div>
            <Input.TextArea
              v-model:value="formData.textContent"
              placeholder="请输入要转换为视频的文本内容"
              :rows="6"
              show-count
              class="form-textarea"
            />
          </div>

          <!-- 音色选择 -->
          <div class="form-section">
            <div class="section-title">音色选择</div>
            <Button
              class="voice-selector-btn"
              :class="{ selected: formData.selectedVoice }"
              @click="openVoiceDrawer"
            >
              {{
                formData.selectedVoice
                  ? getSelectedVoiceFullName()
                  : '点击选择音色'
              }}
            </Button>

            <!-- 试听按钮 -->
            <Button
              class="preview-btn"
              :disabled="!canPreview"
              :loading="previewLoading"
              @click="handlePreviewClick"
            >
              {{ previewButtonText }}
            </Button>

            <!-- 语速和音量调节（仅高保真音色显示） -->
            <div v-if="showVoiceControls" class="voice-controls">
              <!-- 语速调节 -->
              <div class="control-item">
                <div class="control-label">
                  <span>语速</span>
                  <span class="control-value"
                    >{{
                      Math.round(convertSpeedRatio(formData.speechRate) * 10) /
                      10
                    }}x</span
                  >
                </div>
                <Slider
                  :value="Number(formData.speechRate)"
                  @change="
                    (value: any) => (formData.speechRate = value.toString())
                  "
                  :min="0"
                  :max="100"
                  :step="1"
                  class="control-slider"
                />
                <div class="control-tips">
                  <span>慢</span>
                  <span>快</span>
                </div>
              </div>

              <!-- 音量调节 -->
              <div class="control-item">
                <div class="control-label">
                  <span>音量</span>
                  <span class="control-value"
                    >{{
                      Math.round(
                        convertLoudnessRatio(formData.pitchRate) * 10,
                      ) / 10
                    }}x</span
                  >
                </div>
                <Slider
                  :value="Number(formData.pitchRate)"
                  @change="
                    (value: any) => (formData.pitchRate = value.toString())
                  "
                  :min="0"
                  :max="100"
                  :step="1"
                  class="control-slider"
                />
                <div class="control-tips">
                  <span>小</span>
                  <span>大</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 用户协议 -->
          <div class="form-section">
            <Checkbox
              v-model:checked="formData.agreeToTerms"
              class="agreement-checkbox"
              :disabled="configLoading"
            >
              我已阅读并同意
              <a @click="viewUserAgreement" class="agreement-link">
                《视频合成协议》
              </a>
            </Checkbox>
          </div>

          <!-- 生成视频按钮 -->
          <div class="form-section">
            <Button
              type="primary"
              size="large"
              :loading="loading"
              :disabled="!canSubmit"
              class="submit-btn"
              @click="handleSubmit"
            >
              生成视频
            </Button>
          </div>
        </div>
      </Card>
    </div>

    <!-- 数字人选择抽屉 -->
    <Drawer
      v-model:open="avatarDrawerVisible"
      title="选择数字人"
      placement="right"
      :width="600"
      class="avatar-drawer"
    >
      <!-- 线路选择 -->
      <div class="drawer-tabs">
        <Tabs
          v-model:active-key="currentAvatarWayId"
          @change="switchAvatarWay"
          class="way-tabs"
        >
          <Tabs.TabPane v-for="way in wayList" :key="way.id" :tab="way.name" />
        </Tabs>
      </div>

      <!-- 数字人列表 -->
      <div class="drawer-content" v-loading="avatarLoading">
        <div class="avatar-grid">
          <div
            v-for="avatar in avatarList"
            :key="avatar.id"
            class="drawer-avatar-card"
            :class="{
              active: formData.selectedAvatar === avatar.id.toString(),
            }"
            @click="selectAvatar(avatar.id.toString())"
          >
            <div class="avatar-image">
              <img
                v-if="avatar.video_cover"
                :src="avatar.video_cover"
                alt="数字人封面"
                class="avatar-cover"
              />
              <div v-else class="avatar-placeholder">
                <svg
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 7.5V9C15 10.1 14.1 11 13 11S11 10.1 11 9V7.5L5 7V9C5 10.1 4.1 11 3 11S1 10.1 1 9V7C1 6.4 1.4 6 2 6L10 6.5C10 5.1 10.9 4 12 4S14 5.1 14 6.5L22 6C22.6 6 23 6.4 23 7V9C23 10.1 22.1 11 21 11S19 10.1 19 9Z"
                    fill="currentColor"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div
          v-if="!avatarLoading && avatarList.length === 0"
          class="empty-state"
        >
          <p>暂无可用的数字人</p>
          <p>请先在数字人页面创建数字人</p>
        </div>

        <!-- 分页 -->
        <div v-if="avatarPagination.total > 0" class="drawer-pagination">
          <Pagination
            v-model:current="avatarPagination.current"
            :total="avatarPagination.total"
            :page-size="avatarPagination.pageSize"
            :show-size-changer="false"
            :show-quick-jumper="false"
            :show-total="(total) => `共 ${total} 个数字人`"
            @change="handleAvatarPageChange"
          />
        </div>
      </div>
    </Drawer>

    <!-- 音色选择抽屉 -->
    <Drawer
      v-model:open="voiceDrawerVisible"
      title="选择音色"
      placement="right"
      :width="600"
      class="voice-drawer"
    >
      <!-- 音色模式选择 -->
      <div class="drawer-tabs">
        <Tabs
          v-model:active-key="currentVoiceModeId"
          @change="switchVoiceMode"
          class="voice-tabs"
        >
          <Tabs.TabPane
            v-for="mode in voiceModeOptions"
            :key="mode.id"
            :tab="mode.name"
          />
        </Tabs>
      </div>

      <!-- 公共音色按钮（仅在高保真模式下显示） -->
      <div v-if="currentVoiceModeId === 2" class="public-voice-section">
        <Button
          type="primary"
          ghost
          class="public-voice-btn"
          @click="openPublicVoiceModal"
        >
          选择公共音色
        </Button>
      </div>

      <!-- 音色列表 -->
      <div class="drawer-content" v-loading="voiceLoading">
        <div class="voice-grid">
          <div
            v-for="voice in voiceList"
            :key="voice.id"
            class="drawer-voice-item"
            :class="{ active: formData.selectedVoice === voice.id.toString() }"
            @click="selectVoice(voice.id.toString())"
          >
            <div class="voice-thumbnail">
              <img
                src="https://szr.jiajs.cn/index/246.png"
                alt="音色图标"
                class="voice-image"
              />
            </div>
            <div class="voice-info">
              <div class="voice-name">{{ voice.name || voice.title }}</div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!voiceLoading && voiceList.length === 0" class="empty-state">
          <p>暂无可用的音色</p>
          <p>请先在音色克隆页面创建音色</p>
        </div>

        <!-- 分页 -->
        <div v-if="voicePagination.total > 0" class="drawer-pagination">
          <Pagination
            v-model:current="voicePagination.current"
            :total="voicePagination.total"
            :page-size="voicePagination.pageSize"
            :show-size-changer="false"
            :show-quick-jumper="false"
            :show-total="(total) => `共 ${total} 个音色`"
            @change="handleVoicePageChange"
          />
        </div>
      </div>
    </Drawer>

    <!-- 公共音色选择弹窗 -->
    <Modal
      v-model:open="publicVoiceModalVisible"
      title="选择公共音色"
      :width="800"
      :footer="null"
      class="public-voice-modal"
    >
      <!-- 场景筛选 -->
      <div class="scenario-filter">
        <span class="filter-label">场景筛选：</span>
        <Button
          :type="selectedScenario === '' ? 'primary' : 'default'"
          size="small"
          @click="selectedScenario = ''"
          class="scenario-btn"
        >
          全部
        </Button>
        <Button
          v-for="scenario in publicVoiceScenarios"
          :key="scenario"
          :type="selectedScenario === scenario ? 'primary' : 'default'"
          size="small"
          @click="selectedScenario = scenario"
          class="scenario-btn"
        >
          {{ scenario }}
        </Button>
      </div>

      <!-- 公共音色列表 -->
      <div class="public-voice-content" v-loading="publicVoiceLoading">
        <div class="public-voice-grid">
          <div
            v-for="voice in filteredPublicVoiceList"
            :key="voice.code"
            class="public-voice-item"
            :class="{ active: selectedPublicVoice?.code === voice.code }"
          >
            <div class="voice-info">
              <div class="voice-name">{{ voice.resource_display }}</div>
              <div class="voice-details">
                <span class="voice-language">{{ voice.details.language }}</span>
                <span class="voice-scenario">{{
                  voice.details.recommended_scenario
                }}</span>
              </div>
            </div>
            <div class="voice-actions">
              <Button
                size="small"
                @click="toggleAudioPlay(voice.details.demo_link)"
                class="play-btn"
              >
                {{
                  playingAudioUrl === voice.details.demo_link ? '停止' : '试听'
                }}
              </Button>
              <Button
                type="primary"
                size="small"
                @click="selectPublicVoice(voice)"
                class="select-btn"
              >
                选择
              </Button>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  </Page>
</template>

<style lang="scss" scoped>
// 响应式设计
@media (max-width: 768px) {
  .ai-video-creation-container {
    max-width: 100%;
    padding: 16px;
  }

  .form-content {
    gap: 24px;
  }

  .form-input {
    :deep(.ant-input) {
      padding: 10px 12px;
    }
  }

  .form-textarea {
    :deep(.ant-input) {
      min-height: 100px;
      padding: 10px 12px;
    }
  }

  .avatar-selector-box {
    width: 200px;
    height: 360px;
  }

  .avatar-drawer,
  .voice-drawer {
    :deep(.ant-drawer-body) {
      padding: 0;
    }
  }

  .drawer-tabs {
    padding: 0 16px;
  }

  .drawer-content {
    padding: 16px;
  }

  .avatar-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .voice-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .drawer-avatar-card {
    height: 240px;
  }

  .drawer-voice-item {
    .voice-thumbnail {
      height: 140px;
    }
  }
}

.ai-video-creation-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-width: 800px;
  padding: 24px;
  margin: 0 auto;
}

.form-card {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 12px;
  box-shadow: 0 4px 12px hsl(var(--shadow) / 8%);
}

.back-button {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 8px 12px;
  font-size: 16px;
  font-weight: 600;
  color: hsl(var(--foreground));
  transition: all 0.3s ease;

  &:hover {
    color: hsl(var(--primary));
    background: hsl(var(--primary) / 5%);
  }

  .back-icon {
    width: 20px;
    height: 20px;
  }
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 16px;

  .section-title {
    margin-bottom: 8px;
    font-size: 16px;
    font-weight: 600;
    color: hsl(var(--foreground));
  }
}

.section-title-with-action {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;

  .section-title {
    margin-bottom: 0;
  }

  .ai-rewrite-btn {
    height: auto;
    padding: 0;
    font-size: 14px;
    color: hsl(var(--primary));

    &:hover {
      color: hsl(var(--primary) / 80%);
    }
  }
}

// 数字人选择框样式
.avatar-selector-box {
  display: flex;
  flex-direction: column;
  width: 240px;
  height: 427px;
  overflow: hidden;
  cursor: pointer;
  background: hsl(var(--background));
  border: 2px solid hsl(var(--border));
  border-radius: 12px;
  transition: all 0.3s ease;

  &:hover {
    border-color: hsl(var(--primary) / 50%);
    box-shadow: 0 4px 12px hsl(var(--shadow) / 15%);
  }

  .selector-placeholder {
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 16px;
    align-items: center;
    justify-content: center;

    .placeholder-icon {
      width: 80px;
      height: 80px;
      color: hsl(var(--muted-foreground));

      svg {
        width: 100%;
        height: 100%;
      }
    }

    .placeholder-text {
      font-size: 16px;
      color: hsl(var(--muted-foreground));
    }
  }

  .selected-avatar {
    display: flex;
    flex: 1;
    flex-direction: column;

    .avatar-preview {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      background: hsl(var(--muted) / 30%);

      .avatar-image {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;

        .avatar-cover {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .avatar-placeholder {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 100%;

          .placeholder-text {
            font-size: 14px;
            color: hsl(var(--muted-foreground));
          }
        }
      }
    }

    .avatar-name {
      padding: 16px;
      font-size: 14px;
      font-weight: 500;
      color: hsl(var(--foreground));
      text-align: center;
      background: hsl(var(--card));
      border-top: 1px solid hsl(var(--border));
    }
  }
}

// 音色选择按钮样式
.voice-selector-btn {
  width: 100%;
  height: 48px;
  font-size: 14px;
  color: hsl(var(--foreground));
  background: hsl(var(--background));
  border-color: hsl(var(--border));
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    color: hsl(var(--primary));
    border-color: hsl(var(--primary) / 50%);
  }

  &.selected {
    color: hsl(var(--primary));
    background: hsl(var(--primary) / 5%);
    border-color: hsl(var(--primary));
  }
}

// 试听按钮样式
.preview-btn {
  width: 100%;
  height: 40px;
  margin-top: 12px;
  font-size: 14px;
  color: hsl(var(--foreground));
  background: hsl(var(--background));
  border: 1px solid hsl(var(--border));
  border-radius: 6px;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    color: hsl(var(--primary));
    background: hsl(var(--primary) / 5%);
    border-color: hsl(var(--primary));
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  &.ant-btn-loading {
    color: hsl(var(--primary));
    border-color: hsl(var(--primary));
  }
}

// 语速音量控件样式
.voice-controls {
  padding: 16px;
  margin-top: 16px;
  background: hsl(var(--muted) / 30%);
  border: 1px solid hsl(var(--border));
  border-radius: 8px;

  .control-item {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }

    .control-label {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;
      font-size: 14px;
      font-weight: 500;
      color: hsl(var(--foreground));

      .control-value {
        padding: 2px 8px;
        font-size: 12px;
        color: hsl(var(--primary));
        background: hsl(var(--primary) / 10%);
        border-radius: 4px;
      }
    }

    .control-slider {
      margin-bottom: 8px;

      :deep(.ant-slider-rail) {
        background: hsl(var(--border));
      }

      :deep(.ant-slider-track) {
        background: hsl(var(--primary));
      }

      :deep(.ant-slider-handle) {
        border-color: hsl(var(--primary));

        &:hover,
        &:focus {
          border-color: hsl(var(--primary));
          box-shadow: 0 0 0 5px hsl(var(--primary) / 10%);
        }
      }
    }

    .control-tips {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: hsl(var(--muted-foreground));
    }
  }
}

.form-input {
  :deep(.ant-input) {
    padding: 12px 16px;
    font-size: 14px;
    color: hsl(var(--foreground));
    background: hsl(var(--background));
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      border-color: hsl(var(--primary) / 50%);
    }

    &:focus {
      border-color: hsl(var(--primary));
      box-shadow: 0 0 0 2px hsl(var(--primary) / 20%);
    }

    &::placeholder {
      color: hsl(var(--muted-foreground));
    }
  }

  :deep(.ant-input-suffix) {
    font-size: 12px;
    color: hsl(var(--muted-foreground));
  }
}

.form-textarea {
  :deep(.ant-input) {
    min-height: 120px;
    padding: 12px 16px;
    font-size: 14px;
    color: hsl(var(--foreground));
    resize: vertical;
    background: hsl(var(--background));
    border: 1px solid hsl(var(--border));
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      border-color: hsl(var(--primary) / 50%);
    }

    &:focus {
      border-color: hsl(var(--primary));
      box-shadow: 0 0 0 2px hsl(var(--primary) / 20%);
    }

    &::placeholder {
      color: hsl(var(--muted-foreground));
    }
  }

  :deep(.ant-input-data-count) {
    font-size: 12px;
    color: hsl(var(--muted-foreground));
  }
}

.agreement-checkbox {
  font-size: 14px;

  .agreement-link {
    color: hsl(var(--primary));
    text-decoration: none;
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }

  :deep(.ant-checkbox-wrapper) {
    font-size: 14px;
    color: hsl(var(--foreground));

    .ant-checkbox {
      .ant-checkbox-inner {
        background: hsl(var(--background));
        border-color: hsl(var(--border));
      }

      &.ant-checkbox-checked .ant-checkbox-inner {
        background: hsl(var(--primary));
        border-color: hsl(var(--primary));
      }
    }
  }
}

.submit-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  background: hsl(var(--primary));
  border-color: hsl(var(--primary));
  border-radius: 8px;

  &:hover:not(:disabled) {
    background: hsl(var(--primary) / 90%);
    border-color: hsl(var(--primary) / 90%);
  }

  &:disabled {
    color: hsl(var(--muted-foreground));
    background: hsl(var(--muted));
    border-color: hsl(var(--muted));
  }
}

// 抽屉样式
.avatar-drawer,
.voice-drawer {
  :deep(.ant-drawer-content) {
    background: hsl(var(--background));
  }

  :deep(.ant-drawer-header) {
    background: hsl(var(--card));
    border-bottom: 1px solid hsl(var(--border));

    .ant-drawer-title {
      font-weight: 600;
      color: hsl(var(--foreground));
    }
  }

  :deep(.ant-drawer-body) {
    padding: 24px;
    background: hsl(var(--background));
  }
}

.drawer-tabs {
  padding: 0 24px;
  border-bottom: 1px solid hsl(var(--border));

  .way-tabs,
  .voice-tabs {
    :deep(.ant-tabs-nav) {
      margin-bottom: 0;
    }

    :deep(.ant-tabs-tab) {
      color: hsl(var(--muted-foreground));

      &.ant-tabs-tab-active {
        color: hsl(var(--primary));
      }
    }

    :deep(.ant-tabs-ink-bar) {
      background: hsl(var(--primary));
    }
  }
}

.drawer-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 24px;
}

.avatar-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.voice-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.drawer-avatar-card {
  width: 100%;
  height: 320px; // 按照240px*427px等比例缩小到180px*320px
  overflow: hidden;
  cursor: pointer;
  background: hsl(var(--card));
  border: 2px solid hsl(var(--border));
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    border-color: hsl(var(--primary) / 50%);
    box-shadow: 0 4px 12px hsl(var(--shadow) / 15%);
  }

  &.active {
    border-color: hsl(var(--primary));
    box-shadow: 0 4px 12px hsl(var(--primary) / 20%);
  }

  .avatar-image {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: hsl(var(--muted) / 30%);

    .avatar-cover {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .avatar-placeholder {
      width: 60px;
      height: 60px;
      color: hsl(var(--muted-foreground));

      svg {
        width: 100%;
        height: 100%;
      }
    }
  }
}

.drawer-voice-item {
  position: relative;
  overflow: hidden;
  cursor: pointer;
  background: hsl(var(--card));
  border: 2px solid hsl(var(--border));
  border-radius: 12px;
  transition: all 0.3s ease;

  &:hover {
    border-color: hsl(var(--primary) / 50%);
    box-shadow: 0 4px 12px hsl(var(--shadow) / 15%);
  }

  &.active {
    border-color: hsl(var(--primary));
    box-shadow: 0 4px 12px hsl(var(--primary) / 20%);
  }

  .voice-thumbnail {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 180px; // 参考数字人页面音色的240px，等比例缩小
    overflow: hidden;
    background: linear-gradient(
      135deg,
      hsl(var(--primary) / 8%) 0%,
      hsl(var(--primary) / 12%) 100%
    );

    .voice-image {
      width: 80px;
      height: 80px;
      object-fit: contain;
    }
  }

  .voice-info {
    padding: 12px;
    background: hsl(var(--card));

    .voice-name {
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 14px;
      font-weight: 500;
      color: hsl(var(--foreground));
      text-align: center;
      white-space: nowrap;
    }
  }
}

.empty-state {
  padding: 40px 20px;
  color: hsl(var(--muted-foreground));
  text-align: center;

  p {
    margin: 8px 0;
    font-size: 14px;
  }
}

.drawer-pagination {
  display: flex;
  justify-content: center;
  padding-top: 16px;
  border-top: 1px solid hsl(var(--border));

  :deep(.ant-pagination) {
    .ant-pagination-item {
      background: hsl(var(--background));
      border-color: hsl(var(--border));

      a {
        color: hsl(var(--foreground));
      }

      &.ant-pagination-item-active {
        background: hsl(var(--primary));
        border-color: hsl(var(--primary));

        a {
          color: hsl(var(--primary-foreground));
        }
      }
    }

    .ant-pagination-prev,
    .ant-pagination-next {
      background: hsl(var(--background));
      border-color: hsl(var(--border));

      .ant-pagination-item-link {
        color: hsl(var(--foreground));
        background: transparent;
        border: none;
      }

      &:hover {
        border-color: hsl(var(--primary));

        .ant-pagination-item-link {
          color: hsl(var(--primary));
        }
      }
    }

    .ant-pagination-total-text {
      color: hsl(var(--muted-foreground));
    }
  }
}

// 公共音色按钮样式
.public-voice-section {
  padding: 16px;
  border-bottom: 1px solid hsl(var(--border));

  .public-voice-btn {
    width: 100%;
    height: 40px;
    font-size: 14px;
  }
}

// 公共音色弹窗样式
.public-voice-modal {
  .scenario-filter {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    padding-bottom: 16px;
    margin-bottom: 20px;
    border-bottom: 1px solid hsl(var(--border));

    .filter-label {
      margin-right: 8px;
      font-size: 14px;
      font-weight: 500;
      color: hsl(var(--foreground));
    }

    .scenario-btn {
      height: 28px;
      font-size: 12px;
    }
  }

  .public-voice-content {
    max-height: 500px;
    overflow-y: auto;

    .public-voice-grid {
      display: grid;
      grid-template-columns: 1fr;
      gap: 12px;

      .public-voice-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        background: hsl(var(--background));
        border: 1px solid hsl(var(--border));
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
          background: hsl(var(--primary) / 5%);
          border-color: hsl(var(--primary));
        }

        &.active {
          background: hsl(var(--primary) / 10%);
          border-color: hsl(var(--primary));
        }

        .voice-info {
          flex: 1;

          .voice-name {
            margin-bottom: 8px;
            font-size: 16px;
            font-weight: 500;
            color: hsl(var(--foreground));
          }

          .voice-details {
            display: flex;
            gap: 12px;

            .voice-language,
            .voice-scenario {
              padding: 2px 8px;
              font-size: 12px;
              color: hsl(var(--muted-foreground));
              background: hsl(var(--muted) / 50%);
              border-radius: 4px;
            }
          }
        }

        .voice-actions {
          display: flex;
          gap: 8px;

          .play-btn,
          .select-btn {
            height: 32px;
            font-size: 12px;
          }
        }
      }
    }
  }
}
</style>
